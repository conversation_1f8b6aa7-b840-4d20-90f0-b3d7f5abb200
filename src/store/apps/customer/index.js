// ** Redux Imports
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

// ** Axios Imports
import axios from 'axios'

// ** Fetch Customers
export const fetchData = createAsyncThunk('appCustomers/fetchData', async params => {
  const token = window.localStorage.getItem('accessToken')
  const response = await axios.get('/api/crm/customers', {
    headers: {
      Authorization: token
    },
    params
  })

  return response.data
})

// ** Add Customer
export const addCustomer = createAsyncThunk(
  'appCustomers/addCustomer',
  async (data, { getState, dispatch }) => {
    const token = window.localStorage.getItem('accessToken')
    const response = await axios.post('/api/crm/customers', data, {
      headers: {
        Authorization: token
      }
    })
    dispatch(fetchData(getState().customer.params))

    return response.data
  }
)

// ** Delete Customer
export const deleteCustomer = createAsyncThunk(
  'appCustomers/deleteCustomer',
  async (id, { getState, dispatch }) => {
    const token = window.localStorage.getItem('accessToken')
    const response = await axios.delete(`/api/crm/customers?id=${id}`, {
      headers: {
        Authorization: token
      }
    })
    dispatch(fetchData(getState().customer.params))

    return response.data
  }
)

export const appCustomersSlice = createSlice({
  name: 'appCustomers',
  initialState: {
    data: [],
    total: 1,
    params: {},
    allData: []
  },
  reducers: {},
  extraReducers: builder => {
    builder.addCase(fetchData.fulfilled, (state, action) => {
      state.data = action.payload.customers || []
      state.total = action.payload.total || action.payload.customers?.length || 0
      state.params = action.payload.params
      state.allData = action.payload.customers || []
    })
  }
})

export default appCustomersSlice.reducer
