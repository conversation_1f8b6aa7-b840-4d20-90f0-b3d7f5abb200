// ** MongoDB Connection
import { MongoClient, ObjectId } from 'mongodb'

const uri = process.env.MONGODB_URI
const options = {}

let client
let clientPromise

if (process.env.NODE_ENV === 'development') {
  // Development modunda global variable kullan
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options)
    global._mongoClientPromise = client.connect()
  }
  clientPromise = global._mongoClientPromise
} else {
  // Production modunda yeni client oluştur
  client = new MongoClient(uri, options)
  clientPromise = client.connect()
}

export default clientPromise

// ** Database helper functions
export async function getDatabase() {
  const client = await clientPromise
  return client.db(process.env.MONGODB_DB_NAME || 'latek_crm')
}

export async function getCollection(collectionName) {
  const db = await getDatabase()
  return db.collection(collectionName)
}

// ** Common database operations
export const dbOperations = {
  // ** Customers
  async getCustomers(filter = {}) {
    const collection = await getCollection('customers')
    return await collection.find(filter).toArray()
  },

  async createCustomer(customerData) {
    const collection = await getCollection('customers')
    const result = await collection.insertOne({
      ...customerData,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    return result
  },

  async updateCustomer(id, updateData) {
    const collection = await getCollection('customers')
    return await collection.updateOne(
      { _id: new ObjectId(id) },
      {
        $set: {
          ...updateData,
          updatedAt: new Date()
        }
      }
    )
  },

  // ** Content Management
  async getPages(filter = {}) {
    const collection = await getCollection('pages')
    return await collection.find(filter).toArray()
  },

  async createPage(pageData) {
    const collection = await getCollection('pages')
    return await collection.insertOne({
      ...pageData,
      createdAt: new Date(),
      updatedAt: new Date()
    })
  }
}
