// ** MongoDB Connection Test API
import { getDatabase, getCollection } from 'src/lib/mongodb'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // ** Test database connection
    const db = await getDatabase()
    
    // ** Test collection operations
    const testCollection = await getCollection('test')
    
    // ** Insert a test document
    const testDoc = {
      message: 'MongoDB connection successful!',
      timestamp: new Date(),
      environment: process.env.NODE_ENV
    }
    
    const insertResult = await testCollection.insertOne(testDoc)
    
    // ** Read the document back
    const foundDoc = await testCollection.findOne({ _id: insertResult.insertedId })
    
    // ** Clean up - delete the test document
    await testCollection.deleteOne({ _id: insertResult.insertedId })
    
    // ** Return success response
    res.status(200).json({
      success: true,
      message: 'MongoDB connection test successful!',
      database: db.databaseName,
      testDocument: foundDoc,
      timestamp: new Date()
    })
    
  } catch (error) {
    console.error('MongoDB connection error:', error)
    res.status(500).json({
      success: false,
      message: 'MongoDB connection failed',
      error: error.message
    })
  }
}
