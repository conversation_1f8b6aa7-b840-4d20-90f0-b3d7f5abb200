// ** Next.js API Route
import jwt from 'jsonwebtoken'

// ** Mock users (gerçek projede database'den gelecek)
const users = [
  {
    id: 1,
    role: 'admin',
    password: 'admin',
    fullName: '<PERSON>',
    username: 'joh<PERSON><PERSON>',
    email: '<EMAIL>'
  },
  {
    id: 2,
    role: 'client',
    password: 'client',
    fullName: '<PERSON>',
    username: 'j<PERSON><PERSON><PERSON>',
    email: '<EMAIL>'
  }
]

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key',
  expirationTime: process.env.NEXT_PUBLIC_JWT_EXPIRATION || '1d'
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { email, password } = req.body

    // Kullanıcıyı bul
    const user = users.find(u => u.email === email && u.password === password)
    
    if (!user) {
      return res.status(400).json({ 
        error: { email: ['Email or Password is Invalid'] }
      })
    }

    // JWT token oluştur
    const accessToken = jwt.sign(
      { id: user.id }, 
      jwtConfig.secret, 
      { expiresIn: jwtConfig.expirationTime }
    )

    // Response
    const response = {
      accessToken,
      userData: { ...user, password: undefined }
    }

    res.status(200).json(response)
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' })
  }
}
