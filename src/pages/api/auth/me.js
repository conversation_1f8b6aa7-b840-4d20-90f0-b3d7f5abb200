// ** Next.js API Route
import jwt from 'jsonwebtoken'

// ** Mock users (gerçek projede database'den gelecek)
const users = [
  {
    id: 1,
    role: 'admin',
    password: 'admin',
    fullName: '<PERSON>',
    username: 'joh<PERSON><PERSON>',
    email: '<EMAIL>'
  },
  {
    id: 2,
    role: 'client',
    password: 'client',
    fullName: '<PERSON>',
    username: 'j<PERSON><PERSON><PERSON>',
    email: '<EMAIL>'
  }
]

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key',
  expirationTime: process.env.NEXT_PUBLIC_JWT_EXPIRATION || '1d'
}

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Token'ı header'dan al
    const token = req.headers.authorization

    if (!token) {
      return res.status(401).json({ error: 'No token provided' })
    }

    // Token'ı verify et
    const decoded = jwt.verify(token, jwtConfig.secret)
    const userId = decoded.id

    // Kullanıcıyı bul
    const user = users.find(u => u.id === userId)
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid user' })
    }

    // Password'ü çıkar ve response döndür
    const userData = { ...user, password: undefined }
    res.status(200).json({ userData })

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' })
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' })
    }
    res.status(500).json({ error: 'Internal server error' })
  }
}
