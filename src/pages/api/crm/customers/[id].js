// ** Next.js API Route - Customer Detail
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb'
import { getCollection } from 'src/lib/mongodb'

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key'
}

// ** Auth middleware
const authenticateToken = (req) => {
  const token = req.headers.authorization
  if (!token) throw new Error('No token provided')
  
  return jwt.verify(token, jwtConfig.secret)
}

export default async function handler(req, res) {
  try {
    // ** Authentication check
    const decoded = authenticateToken(req)
    
    const { id } = req.query

    if (!id) {
      return res.status(400).json({ error: 'Customer ID is required' })
    }

    switch (req.method) {
      case 'GET':
        // ** Get customer details
        const customersCollection = await getCollection('customers')
        
        try {
          const customer = await customersCollection.findOne({ _id: new ObjectId(id) })
          
          if (!customer) {
            return res.status(404).json({ error: 'Customer not found' })
          }

          // ** Get customer activity (mock data for now)
          const activity = [
            {
              id: 1,
              type: 'email',
              action: 'Email sent: Welcome to our service',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
              details: {
                subject: 'Welcome to our service',
                status: 'sent'
              }
            },
            {
              id: 2,
              type: 'call',
              action: 'Phone call completed',
              timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
              details: {
                duration: '15 minutes',
                outcome: 'positive'
              }
            },
            {
              id: 3,
              type: 'meeting',
              action: 'Meeting scheduled for next week',
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
              details: {
                type: 'demo',
                scheduled: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
              }
            },
            {
              id: 4,
              type: 'note',
              action: 'Note added: Customer interested in premium features',
              timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
              details: {
                note: 'Customer showed interest in premium features during our conversation'
              }
            }
          ]

          // ** Get customer deals (mock data for now)
          const deals = [
            {
              id: 1,
              title: 'Premium Service Package',
              value: 15000,
              status: 'negotiation',
              probability: 75,
              expectedCloseDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
              createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            {
              id: 2,
              title: 'Consulting Services',
              value: 8500,
              status: 'proposal',
              probability: 60,
              expectedCloseDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
              createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
            }
          ]

          // ** Get email history (mock data for now)
          const emails = [
            {
              id: 1,
              subject: 'Welcome to our service',
              type: 'outbound',
              status: 'sent',
              sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
              openedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
              clickedAt: null
            },
            {
              id: 2,
              subject: 'Thank you for your interest',
              type: 'outbound',
              status: 'sent',
              sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
              openedAt: new Date(Date.now() - 23 * 60 * 60 * 1000),
              clickedAt: new Date(Date.now() - 22 * 60 * 60 * 1000)
            },
            {
              id: 3,
              subject: 'Re: Product inquiry',
              type: 'inbound',
              status: 'received',
              receivedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            }
          ]

          // ** Calculate customer metrics
          const totalDeals = deals.length
          const totalDealValue = deals.reduce((sum, deal) => sum + deal.value, 0)
          const totalInteractions = activity.length + emails.length

          const customerDetails = {
            ...customer,
            metrics: {
              totalDeals,
              totalDealValue,
              totalInteractions,
              averageDealValue: totalDeals > 0 ? Math.round(totalDealValue / totalDeals) : 0,
              lastContactDate: activity.length > 0 ? activity[0].timestamp : customer.createdAt
            },
            activity: activity.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)),
            deals: deals.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)),
            emails: emails.sort((a, b) => {
              const dateA = a.sentAt || a.receivedAt
              const dateB = b.sentAt || b.receivedAt
              return new Date(dateB) - new Date(dateA)
            })
          }

          res.status(200).json({ customer: customerDetails })
        } catch (error) {
          if (error.message.includes('ObjectId')) {
            return res.status(400).json({ error: 'Invalid customer ID format' })
          }
          throw error
        }
        break

      case 'PUT':
        // ** Update customer
        const updateData = req.body
        const customersCollectionUpdate = await getCollection('customers')
        
        try {
          const result = await customersCollectionUpdate.updateOne(
            { _id: new ObjectId(id) },
            { 
              $set: { 
                ...updateData, 
                updatedAt: new Date() 
              } 
            }
          )

          if (result.matchedCount === 0) {
            return res.status(404).json({ error: 'Customer not found' })
          }

          res.status(200).json({ message: 'Customer updated successfully' })
        } catch (error) {
          if (error.message.includes('ObjectId')) {
            return res.status(400).json({ error: 'Invalid customer ID format' })
          }
          throw error
        }
        break

      case 'DELETE':
        // ** Delete customer
        const customersCollectionDelete = await getCollection('customers')
        
        try {
          const result = await customersCollectionDelete.deleteOne({ _id: new ObjectId(id) })

          if (result.deletedCount === 0) {
            return res.status(404).json({ error: 'Customer not found' })
          }

          res.status(200).json({ message: 'Customer deleted successfully' })
        } catch (error) {
          if (error.message.includes('ObjectId')) {
            return res.status(400).json({ error: 'Invalid customer ID format' })
          }
          throw error
        }
        break

      default:
        res.status(405).json({ message: 'Method not allowed' })
    }

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.message === 'No token provided') {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    console.error('Customer Detail API Error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
