// ** Next.js API Route - CRM Reports
import jwt from 'jsonwebtoken'
import { getCollection } from 'src/lib/mongodb'

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key'
}

// ** Auth middleware
const authenticateToken = (req) => {
  const token = req.headers.authorization
  if (!token) throw new Error('No token provided')
  
  return jwt.verify(token, jwtConfig.secret)
}

export default async function handler(req, res) {
  try {
    // ** Authentication check
    const decoded = authenticateToken(req)
    
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' })
    }

    // ** Get customers for analysis
    const customersCollection = await getCollection('customers')
    const customers = await customersCollection.find({}).toArray()

    // ** Generate customer growth data (last 6 months)
    const customerGrowth = []
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
    
    months.forEach((month, index) => {
      // Mock data based on actual customer count
      const baseCount = Math.floor(customers.length / 6)
      const variation = Math.floor(Math.random() * 10) - 5
      customerGrowth.push({
        month,
        count: Math.max(1, baseCount + variation + index * 2)
      })
    })

    // ** Generate deal status data
    const dealStatus = [
      { status: 'Won', count: Math.floor(customers.length * 0.6) },
      { status: 'Lost', count: Math.floor(customers.length * 0.2) },
      { status: 'Pending', count: Math.floor(customers.length * 0.2) }
    ]

    // ** Generate revenue data
    const revenueData = []
    months.forEach((month, index) => {
      const baseRevenue = 15000
      const growth = index * 3000
      const variation = Math.floor(Math.random() * 5000) - 2500
      revenueData.push({
        month,
        amount: Math.max(10000, baseRevenue + growth + variation)
      })
    })

    // ** Calculate additional metrics
    const totalCustomers = customers.length
    const activeCustomers = customers.filter(c => c.status === 'active').length
    const prospects = customers.filter(c => c.status === 'prospect').length
    
    // ** Customer acquisition by source
    const acquisitionSources = [
      { source: 'Website', count: Math.floor(totalCustomers * 0.4) },
      { source: 'Referral', count: Math.floor(totalCustomers * 0.3) },
      { source: 'Social Media', count: Math.floor(totalCustomers * 0.2) },
      { source: 'Direct', count: Math.floor(totalCustomers * 0.1) }
    ]

    // ** Top customers (mock data)
    const topCustomers = customers.slice(0, 5).map((customer, index) => ({
      name: customer.name,
      company: customer.company,
      revenue: (5000 - index * 500) + Math.floor(Math.random() * 2000),
      deals: Math.floor(Math.random() * 5) + 1
    }))

    // ** Monthly performance
    const monthlyPerformance = months.map((month, index) => ({
      month,
      newCustomers: customerGrowth[index].count,
      revenue: revenueData[index].amount,
      deals: Math.floor(Math.random() * 20) + 10,
      conversionRate: Math.floor(Math.random() * 30) + 60
    }))

    const reportData = {
      // ** Overview metrics
      overview: {
        totalCustomers,
        activeCustomers,
        prospects,
        totalRevenue: revenueData.reduce((sum, item) => sum + item.amount, 0),
        averageDealValue: Math.floor(revenueData.reduce((sum, item) => sum + item.amount, 0) / dealStatus.reduce((sum, item) => sum + item.count, 0)),
        conversionRate: Math.floor((dealStatus.find(d => d.status === 'Won')?.count || 0) / dealStatus.reduce((sum, item) => sum + item.count, 0) * 100)
      },

      // ** Chart data
      customers: customerGrowth,
      deals: dealStatus,
      revenue: revenueData,
      
      // ** Additional analytics
      acquisitionSources,
      topCustomers,
      monthlyPerformance,

      // ** KPIs
      kpis: {
        customerSatisfaction: 85,
        dealConversionRate: 73,
        averageDealValue: 2450,
        averageSalesCycle: 18
      },

      // ** Trends
      trends: {
        customerGrowthRate: '+15%',
        revenueGrowthRate: '+23%',
        dealVelocity: '+8%',
        churnRate: '2.3%'
      }
    }

    res.status(200).json(reportData)

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.message === 'No token provided') {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    console.error('Reports API Error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
