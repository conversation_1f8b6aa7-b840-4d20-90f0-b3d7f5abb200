// ** Next.js API Route - Email Integration
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb'
import { getCollection } from 'src/lib/mongodb'
import nodemailer from 'nodemailer'

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key'
}

// ** Auth middleware
const authenticateToken = (req) => {
  const token = req.headers.authorization
  if (!token) throw new Error('No token provided')
  
  return jwt.verify(token, jwtConfig.secret)
}

// ** Email transporter configuration
const createTransporter = () => {
  // For development, we'll use a mock transporter
  // In production, configure with real SMTP settings
  if (process.env.NODE_ENV === 'development') {
    return {
      sendMail: async (mailOptions) => {
        console.log('📧 Mock Email Sent:', {
          to: mailOptions.to,
          subject: mailOptions.subject,
          text: mailOptions.text?.substring(0, 100) + '...',
          html: mailOptions.html ? 'HTML content included' : 'No HTML'
        })
        
        // Simulate email sending delay
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        return {
          messageId: `mock-${Date.now()}@example.com`,
          response: '250 Message accepted'
        }
      }
    }
  }

  // Production email configuration
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  })
}

export default async function handler(req, res) {
  try {
    // ** Authentication check
    const decoded = authenticateToken(req)
    
    if (req.method !== 'POST') {
      return res.status(405).json({ message: 'Method not allowed' })
    }

    const { 
      customerId, 
      to, 
      subject, 
      message, 
      template,
      attachments = []
    } = req.body

    // ** Validation
    if (!to || !subject || !message) {
      return res.status(400).json({ 
        error: 'Missing required fields: to, subject, message' 
      })
    }

    // ** Get customer details if customerId provided
    let customer = null
    if (customerId) {
      const customersCollection = await getCollection('customers')
      customer = await customersCollection.findOne({ _id: new ObjectId(customerId) })
    }

    // ** Prepare email content
    let emailContent = message
    let emailSubject = subject

    // ** Apply template if specified
    if (template && customer) {
      const templates = {
        welcome: {
          subject: `Welcome to our service, ${customer.name}!`,
          content: `
            <h2>Welcome ${customer.name}!</h2>
            <p>Thank you for choosing our service. We're excited to work with ${customer.company}.</p>
            <p>If you have any questions, please don't hesitate to reach out.</p>
            <p>Best regards,<br>Your CRM Team</p>
          `
        },
        followup: {
          subject: `Following up on our conversation`,
          content: `
            <h2>Hi ${customer.name},</h2>
            <p>I wanted to follow up on our recent conversation regarding ${customer.company}'s needs.</p>
            <p>${message}</p>
            <p>Looking forward to hearing from you.</p>
            <p>Best regards,<br>Your CRM Team</p>
          `
        },
        proposal: {
          subject: `Proposal for ${customer.company}`,
          content: `
            <h2>Dear ${customer.name},</h2>
            <p>Please find attached our proposal for ${customer.company}.</p>
            <p>${message}</p>
            <p>We look forward to discussing this opportunity with you.</p>
            <p>Best regards,<br>Your CRM Team</p>
          `
        }
      }

      if (templates[template]) {
        emailSubject = templates[template].subject
        emailContent = templates[template].content
      }
    }

    // ** Create email transporter
    const transporter = createTransporter()

    // ** Email options
    const mailOptions = {
      from: process.env.SMTP_FROM || '<EMAIL>',
      to: to,
      subject: emailSubject,
      text: emailContent.replace(/<[^>]*>/g, ''), // Strip HTML for text version
      html: emailContent,
      attachments: attachments.map(att => ({
        filename: att.filename,
        content: att.content,
        contentType: att.contentType
      }))
    }

    // ** Send email
    const result = await transporter.sendMail(mailOptions)

    // ** Save email to database
    const emailsCollection = await getCollection('emails')
    const emailRecord = {
      customerId: customerId ? new ObjectId(customerId) : null,
      to: to,
      subject: emailSubject,
      content: emailContent,
      template: template || null,
      status: 'sent',
      messageId: result.messageId,
      sentAt: new Date(),
      sentBy: decoded.id,
      attachments: attachments.map(att => ({
        filename: att.filename,
        size: att.size,
        contentType: att.contentType
      })),
      tracking: {
        opened: false,
        openedAt: null,
        clicked: false,
        clickedAt: null,
        bounced: false,
        bouncedAt: null
      }
    }

    const insertResult = await emailsCollection.insertOne(emailRecord)

    // ** Update customer's last contact date
    if (customerId) {
      const customersCollection = await getCollection('customers')
      await customersCollection.updateOne(
        { _id: new ObjectId(customerId) },
        { 
          $set: { 
            lastContactDate: new Date(),
            updatedAt: new Date()
          } 
        }
      )

      // ** Add activity record
      const activitiesCollection = await getCollection('activities')
      await activitiesCollection.insertOne({
        customerId: new ObjectId(customerId),
        type: 'email',
        action: 'Email sent',
        details: {
          subject: emailSubject,
          to: to,
          template: template
        },
        createdAt: new Date(),
        createdBy: decoded.id
      })
    }

    res.status(200).json({
      success: true,
      message: 'Email sent successfully',
      emailId: insertResult.insertedId,
      messageId: result.messageId
    })

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.message === 'No token provided') {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    console.error('Email Send API Error:', error)
    res.status(500).json({ 
      error: 'Failed to send email',
      details: error.message 
    })
  }
}
