// ** Next.js API Route - CRM Customers
import jwt from 'jsonwebtoken'
import { ObjectId } from 'mongodb'
import { dbOperations, getCollection } from 'src/lib/mongodb'

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key'
}

// ** Auth middleware
const authenticateToken = req => {
  const token = req.headers.authorization
  if (!token) throw new Error('No token provided')

  return jwt.verify(token, jwtConfig.secret)
}

export default async function handler(req, res) {
  try {
    // ** Authentication check
    const decoded = authenticateToken(req)

    switch (req.method) {
      case 'GET':
        // ** Get all customers from MongoDB
        const customers = await dbOperations.getCustomers()
        res.status(200).json({ customers })
        break

      case 'POST':
        // ** Create new customer in MongoDB
        const result = await dbOperations.createCustomer(req.body)
        res.status(201).json({
          customer: { _id: result.insertedId, ...req.body }
        })
        break

      case 'PUT':
        // ** Update customer in MongoDB
        const { id } = req.query
        await dbOperations.updateCustomer(id, req.body)
        res.status(200).json({ message: 'Customer updated successfully' })
        break

      case 'DELETE':
        // ** Delete customer from MongoDB
        const deleteId = req.query.id
        const collection = await getCollection('customers')
        await collection.deleteOne({ _id: new ObjectId(deleteId) })
        res.status(200).json({ message: 'Customer deleted successfully' })
        break

      default:
        res.status(405).json({ message: 'Method not allowed' })
    }
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.message === 'No token provided') {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    res.status(500).json({ error: 'Internal server error' })
  }
}
