// ** Next.js API Route - CRM Customers
import jwt from 'jsonwebtoken'

// ** Mock customers data (gerçek projede database'den gelecek)
let customers = [
  {
    id: 1,
    name: '<PERSON><PERSON>ı<PERSON>z',
    email: '<EMAIL>',
    phone: '+90 ************',
    company: 'ABC Şirketi',
    status: 'active',
    createdAt: '2024-01-15',
    lastContact: '2024-01-20'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+90 ************',
    company: 'XYZ Ltd.',
    status: 'prospect',
    createdAt: '2024-01-10',
    lastContact: '2024-01-18'
  }
]

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key'
}

// ** Auth middleware
const authenticateToken = (req) => {
  const token = req.headers.authorization
  if (!token) throw new Error('No token provided')
  
  return jwt.verify(token, jwtConfig.secret)
}

export default async function handler(req, res) {
  try {
    // ** Authentication check
    const decoded = authenticateToken(req)
    
    switch (req.method) {
      case 'GET':
        // ** Get all customers
        res.status(200).json({ customers })
        break
        
      case 'POST':
        // ** Create new customer
        const newCustomer = {
          id: customers.length + 1,
          ...req.body,
          createdAt: new Date().toISOString().split('T')[0],
          lastContact: new Date().toISOString().split('T')[0]
        }
        customers.push(newCustomer)
        res.status(201).json({ customer: newCustomer })
        break
        
      case 'PUT':
        // ** Update customer
        const { id } = req.query
        const customerIndex = customers.findIndex(c => c.id === parseInt(id))
        
        if (customerIndex === -1) {
          return res.status(404).json({ error: 'Customer not found' })
        }
        
        customers[customerIndex] = { ...customers[customerIndex], ...req.body }
        res.status(200).json({ customer: customers[customerIndex] })
        break
        
      case 'DELETE':
        // ** Delete customer
        const deleteId = req.query.id
        customers = customers.filter(c => c.id !== parseInt(deleteId))
        res.status(200).json({ message: 'Customer deleted successfully' })
        break
        
      default:
        res.status(405).json({ message: 'Method not allowed' })
    }
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.message === 'No token provided') {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    res.status(500).json({ error: 'Internal server error' })
  }
}
