// ** React Imports
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

// ** MUI Imports
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Divider from '@mui/material/Divider'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'
import CardHeader from '@mui/material/CardHeader'
import Button from '@mui/material/Button'
import Chip from '@mui/material/Chip'
import Avatar from '@mui/material/Avatar'
import IconButton from '@mui/material/IconButton'
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components
import CustomChip from 'src/@core/components/mui/chip'
import { getInitials } from 'src/@core/utils/get-initials'

// ** Third Party Imports
import { format } from 'date-fns'

// ** Custom Components
import EmailCompose from 'src/views/crm/emails/EmailCompose'

const CustomerDetailView = () => {
  // ** State
  const [customer, setCustomer] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [showEmailCompose, setShowEmailCompose] = useState(false)

  // ** Hooks
  const router = useRouter()
  const { id } = router.query

  useEffect(() => {
    if (id) {
      fetchCustomerDetails()
    }
  }, [id])

  const fetchCustomerDetails = async () => {
    try {
      const token = window.localStorage.getItem('accessToken')
      const response = await fetch(`/api/crm/customers/${id}`, {
        headers: {
          Authorization: token
        }
      })

      if (response.ok) {
        const data = await response.json()
        setCustomer(data.customer)
      } else {
        console.error('Failed to fetch customer details')
      }
    } catch (error) {
      console.error('Error fetching customer details:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  const statusObj = {
    active: 'success',
    pending: 'warning',
    inactive: 'secondary',
    prospect: 'info'
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Typography>Loading customer details...</Typography>
      </Box>
    )
  }

  if (!customer) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Typography>Customer not found</Typography>
      </Box>
    )
  }

  return (
    <Grid container spacing={6}>
      {/* Customer Header */}
      <Grid item xs={12}>
        <Card>
          <CardContent sx={{ pt: 15, display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
            <Avatar
              skin='light'
              variant='rounded'
              color='primary'
              sx={{ width: 120, height: 120, fontWeight: 600, mb: 4, fontSize: '3rem' }}
            >
              {getInitials(customer.name)}
            </Avatar>
            <Typography variant='h6' sx={{ mb: 2 }}>
              {customer.name}
            </Typography>
            <CustomChip
              skin='light'
              size='small'
              label={customer.status}
              color={statusObj[customer.status]}
              sx={{
                height: 20,
                fontWeight: 600,
                borderRadius: '5px',
                fontSize: '0.875rem',
                textTransform: 'capitalize',
                '& .MuiChip-label': { mt: -0.25 }
              }}
            />
          </CardContent>

          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-around', pt: 4 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Typography variant='h6'>5</Typography>
                <Typography variant='body2'>Total Deals</Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Typography variant='h6'>$25,000</Typography>
                <Typography variant='body2'>Total Revenue</Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Typography variant='h6'>15</Typography>
                <Typography variant='body2'>Interactions</Typography>
              </Box>
            </Box>
          </CardContent>

          <Divider />

          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
              <Button variant='contained' startIcon={<Icon icon='mdi:pencil' />}>
                Edit Customer
              </Button>
              <Button
                variant='outlined'
                startIcon={<Icon icon='mdi:email' />}
                onClick={() => setShowEmailCompose(true)}
              >
                Send Email
              </Button>
              <IconButton>
                <Icon icon='mdi:phone' />
              </IconButton>
              <IconButton>
                <Icon icon='mdi:message' />
              </IconButton>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Customer Details Tabs */}
      <Grid item xs={12}>
        <TabContext value={activeTab}>
          <TabList
            onChange={handleTabChange}
            aria-label='customer details tabs'
            sx={{ borderBottom: theme => `1px solid ${theme.palette.divider}` }}
          >
            <Tab
              value='overview'
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Icon icon='mdi:account-outline' />
                  Overview
                </Box>
              }
            />
            <Tab
              value='activity'
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Icon icon='mdi:timeline-outline' />
                  Activity
                </Box>
              }
            />
            <Tab
              value='deals'
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Icon icon='mdi:handshake' />
                  Deals
                </Box>
              }
            />
            <Tab
              value='emails'
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Icon icon='mdi:email-outline' />
                  Emails
                </Box>
              }
            />
          </TabList>

          {/* Overview Tab */}
          <TabPanel value='overview' sx={{ p: 0 }}>
            <Grid container spacing={6} sx={{ mt: 0 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title='Customer Information' />
                  <CardContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Icon icon='mdi:account' />
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Full Name
                          </Typography>
                          <Typography variant='body1'>{customer.name}</Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Icon icon='mdi:email' />
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Email
                          </Typography>
                          <Typography variant='body1'>{customer.email}</Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Icon icon='mdi:phone' />
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Phone
                          </Typography>
                          <Typography variant='body1'>{customer.phone}</Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Icon icon='mdi:office-building' />
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Company
                          </Typography>
                          <Typography variant='body1'>{customer.company}</Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Icon icon='mdi:calendar' />
                        <Box>
                          <Typography variant='body2' color='text.secondary'>
                            Created Date
                          </Typography>
                          <Typography variant='body1'>
                            {customer.createdAt ? format(new Date(customer.createdAt), 'MMM dd, yyyy') : 'N/A'}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title='Quick Actions' />
                  <CardContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <Button
                        fullWidth
                        variant='outlined'
                        startIcon={<Icon icon='mdi:email-plus' />}
                        sx={{ justifyContent: 'flex-start' }}
                        onClick={() => setShowEmailCompose(true)}
                      >
                        Send Email
                      </Button>
                      <Button
                        fullWidth
                        variant='outlined'
                        startIcon={<Icon icon='mdi:phone-plus' />}
                        sx={{ justifyContent: 'flex-start' }}
                      >
                        Schedule Call
                      </Button>
                      <Button
                        fullWidth
                        variant='outlined'
                        startIcon={<Icon icon='mdi:calendar-plus' />}
                        sx={{ justifyContent: 'flex-start' }}
                      >
                        Schedule Meeting
                      </Button>
                      <Button
                        fullWidth
                        variant='outlined'
                        startIcon={<Icon icon='mdi:note-plus' />}
                        sx={{ justifyContent: 'flex-start' }}
                      >
                        Add Note
                      </Button>
                      <Button
                        fullWidth
                        variant='outlined'
                        startIcon={<Icon icon='mdi:handshake' />}
                        sx={{ justifyContent: 'flex-start' }}
                      >
                        Create Deal
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Activity Tab */}
          <TabPanel value='activity' sx={{ p: 0 }}>
            <Card sx={{ mt: 6 }}>
              <CardHeader title='Recent Activity' />
              <CardContent>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                  {[
                    { type: 'email', action: 'Email sent', time: '2 hours ago', icon: 'mdi:email' },
                    { type: 'call', action: 'Phone call completed', time: '1 day ago', icon: 'mdi:phone' },
                    { type: 'meeting', action: 'Meeting scheduled', time: '3 days ago', icon: 'mdi:calendar' },
                    { type: 'note', action: 'Note added', time: '1 week ago', icon: 'mdi:note' }
                  ].map((activity, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                      <Avatar sx={{ width: 40, height: 40, bgcolor: 'primary.main' }}>
                        <Icon icon={activity.icon} />
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant='body1'>{activity.action}</Typography>
                        <Typography variant='body2' color='text.secondary'>
                          {activity.time}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </TabPanel>

          {/* Deals Tab */}
          <TabPanel value='deals' sx={{ p: 0 }}>
            <Card sx={{ mt: 6 }}>
              <CardHeader title='Customer Deals' />
              <CardContent>
                <Typography variant='body2' color='text.secondary'>
                  Deal management functionality will be implemented here.
                </Typography>
              </CardContent>
            </Card>
          </TabPanel>

          {/* Emails Tab */}
          <TabPanel value='emails' sx={{ p: 0 }}>
            <Card sx={{ mt: 6 }}>
              <CardHeader title='Email History' />
              <CardContent>
                <Typography variant='body2' color='text.secondary'>
                  Email integration functionality will be implemented here.
                </Typography>
              </CardContent>
            </Card>
          </TabPanel>
        </TabContext>
      </Grid>

      {/* Email Compose Modal */}
      {showEmailCompose && (
        <Grid item xs={12} sx={{ mt: 6 }}>
          <EmailCompose
            customer={customer}
            onClose={() => setShowEmailCompose(false)}
            onSent={() => {
              setShowEmailCompose(false)
              // Refresh customer data to update last contact
              fetchCustomerDetails()
            }}
          />
        </Grid>
      )}
    </Grid>
  )
}

export default CustomerDetailView
