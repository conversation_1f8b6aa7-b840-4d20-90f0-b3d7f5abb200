// ** React Imports
import { useState, useEffect } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Button from '@mui/material/Button'

// ** Third Party Imports
import { Bar, Doughnut, Line } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
} from 'chart.js'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components
import CrmStatistics from 'src/views/crm/dashboard/CrmStatistics'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
)

const CrmReports = () => {
  // ** State
  const [dateRange, setDateRange] = useState('30')
  const [reportData, setReportData] = useState({
    customers: [],
    deals: [],
    revenue: []
  })

  useEffect(() => {
    fetchReportData()
  }, [dateRange])

  const fetchReportData = async () => {
    try {
      const token = window.localStorage.getItem('accessToken')
      const response = await fetch('/api/crm/reports', {
        headers: {
          Authorization: token
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setReportData(data)
      }
    } catch (error) {
      console.error('Error fetching report data:', error)
      // Mock data for demo
      setReportData({
        customers: [
          { month: 'Jan', count: 12 },
          { month: 'Feb', count: 19 },
          { month: 'Mar', count: 15 },
          { month: 'Apr', count: 25 },
          { month: 'May', count: 22 },
          { month: 'Jun', count: 30 }
        ],
        deals: [
          { status: 'Won', count: 45 },
          { status: 'Lost', count: 15 },
          { status: 'Pending', count: 25 }
        ],
        revenue: [
          { month: 'Jan', amount: 15000 },
          { month: 'Feb', amount: 22000 },
          { month: 'Mar', amount: 18000 },
          { month: 'Apr', amount: 35000 },
          { month: 'May', amount: 28000 },
          { month: 'Jun', amount: 42000 }
        ]
      })
    }
  }

  // ** Chart Data
  const customerGrowthData = {
    labels: reportData.customers.map(item => item.month),
    datasets: [
      {
        label: 'New Customers',
        data: reportData.customers.map(item => item.count),
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2
      }
    ]
  }

  const dealStatusData = {
    labels: reportData.deals.map(item => item.status),
    datasets: [
      {
        data: reportData.deals.map(item => item.count),
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',
          'rgba(255, 99, 132, 0.8)',
          'rgba(255, 205, 86, 0.8)'
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 205, 86, 1)'
        ],
        borderWidth: 2
      }
    ]
  }

  const revenueData = {
    labels: reportData.revenue.map(item => item.month),
    datasets: [
      {
        label: 'Revenue ($)',
        data: reportData.revenue.map(item => item.amount),
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
      }
    ]
  }

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top'
      },
      title: {
        display: false
      }
    }
  }

  const exportReport = () => {
    // Mock export functionality
    const csvContent = "data:text/csv;charset=utf-8," 
      + "Month,Customers,Revenue\n"
      + reportData.customers.map((item, index) => 
          `${item.month},${item.count},${reportData.revenue[index]?.amount || 0}`
        ).join("\n")
    
    const encodedUri = encodeURI(csvContent)
    const link = document.createElement("a")
    link.setAttribute("href", encodedUri)
    link.setAttribute("download", "crm_report.csv")
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <Grid container spacing={6}>
      {/* Statistics Overview */}
      <Grid item xs={12}>
        <CrmStatistics />
      </Grid>

      {/* Report Controls */}
      <Grid item xs={12}>
        <Card>
          <CardHeader 
            title='Report Filters' 
            action={
              <Box sx={{ display: 'flex', gap: 2 }}>
                <FormControl size='small' sx={{ minWidth: 120 }}>
                  <InputLabel>Date Range</InputLabel>
                  <Select
                    value={dateRange}
                    label='Date Range'
                    onChange={(e) => setDateRange(e.target.value)}
                  >
                    <MenuItem value='7'>Last 7 days</MenuItem>
                    <MenuItem value='30'>Last 30 days</MenuItem>
                    <MenuItem value='90'>Last 3 months</MenuItem>
                    <MenuItem value='365'>Last year</MenuItem>
                  </Select>
                </FormControl>
                <Button
                  variant='contained'
                  startIcon={<Icon icon='mdi:download' />}
                  onClick={exportReport}
                >
                  Export
                </Button>
              </Box>
            }
          />
        </Card>
      </Grid>

      {/* Customer Growth Chart */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardHeader title='Customer Growth' />
          <CardContent>
            <Bar data={customerGrowthData} options={chartOptions} />
          </CardContent>
        </Card>
      </Grid>

      {/* Deal Status Chart */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardHeader title='Deal Status Distribution' />
          <CardContent>
            <Box sx={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Doughnut data={dealStatusData} options={chartOptions} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Revenue Trend */}
      <Grid item xs={12}>
        <Card>
          <CardHeader title='Revenue Trend' />
          <CardContent>
            <Line data={revenueData} options={chartOptions} />
          </CardContent>
        </Card>
      </Grid>

      {/* Key Metrics */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardHeader title='Key Performance Indicators' />
          <CardContent>
            <Grid container spacing={4}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant='h4' color='primary'>
                    85%
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Customer Satisfaction
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant='h4' color='success.main'>
                    73%
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Deal Conversion Rate
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant='h4' color='warning.main'>
                    $2,450
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Average Deal Value
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant='h4' color='info.main'>
                    18 days
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    Average Sales Cycle
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Top Performing Customers */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardHeader title='Top Performing Customers' />
          <CardContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {[
                { name: 'ABC Şirketi', revenue: '$15,000', deals: 5 },
                { name: 'XYZ Ltd.', revenue: '$12,500', deals: 3 },
                { name: 'Tech Corp', revenue: '$10,200', deals: 4 },
                { name: 'Digital Inc', revenue: '$8,750', deals: 2 }
              ].map((customer, index) => (
                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant='body2'>{customer.name}</Typography>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Typography variant='body2' color='success.main'>{customer.revenue}</Typography>
                    <Typography variant='body2' color='text.secondary'>{customer.deals} deals</Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default CrmReports
