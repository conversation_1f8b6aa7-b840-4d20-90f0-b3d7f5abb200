// ** React Imports
import { useState } from 'react'

// ** <PERSON><PERSON> Components
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Alert from '@mui/material/Alert'

// ** Layout Import
import BlankLayout from 'src/@core/layouts/BlankLayout'

const ApiTestPage = () => {
  const [results, setResults] = useState({})
  const [loading, setLoading] = useState({})

  const testEndpoint = async (name, url, method = 'GET', body = null, headers = {}) => {
    setLoading(prev => ({ ...prev, [name]: true }))
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: body ? JSON.stringify(body) : null
      })
      
      const data = await response.json()
      setResults(prev => ({
        ...prev,
        [name]: {
          status: response.status,
          success: response.ok,
          data
        }
      }))
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [name]: {
          status: 'Error',
          success: false,
          data: { error: error.message }
        }
      }))
    }
    setLoading(prev => ({ ...prev, [name]: false }))
  }

  const tests = [
    {
      name: 'MongoDB Connection Test',
      key: 'mongodb',
      action: () => testEndpoint('mongodb', '/api/test-db')
    },
    {
      name: 'Login - Admin',
      key: 'loginAdmin',
      action: () => testEndpoint('loginAdmin', '/api/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'admin'
      })
    },
    {
      name: 'Login - Client',
      key: 'loginClient',
      action: () => testEndpoint('loginClient', '/api/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'client'
      })
    },
    {
      name: 'Login - Invalid',
      key: 'loginInvalid',
      action: () => testEndpoint('loginInvalid', '/api/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'wrong'
      })
    },
    {
      name: 'Get User Info (with token)',
      key: 'userInfo',
      action: () => {
        const adminResult = results.loginAdmin
        if (adminResult?.success && adminResult.data.accessToken) {
          testEndpoint('userInfo', '/api/auth/me', 'GET', null, {
            'Authorization': adminResult.data.accessToken
          })
        } else {
          alert('Please run "Login - Admin" test first to get a token')
        }
      }
    },
    {
      name: 'Get Customers (with token)',
      key: 'customers',
      action: () => {
        const adminResult = results.loginAdmin
        if (adminResult?.success && adminResult.data.accessToken) {
          testEndpoint('customers', '/api/crm/customers', 'GET', null, {
            'Authorization': adminResult.data.accessToken
          })
        } else {
          alert('Please run "Login - Admin" test first to get a token')
        }
      }
    }
  ]

  return (
    <Box sx={{ p: 6 }}>
      <Typography variant='h4' sx={{ mb: 6 }}>
        🔐 Authentication & API Test Dashboard
      </Typography>
      
      <Box sx={{ display: 'grid', gap: 4, gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))' }}>
        {tests.map((test) => (
          <Card key={test.key}>
            <CardHeader title={test.name} />
            <CardContent>
              <Button
                variant='contained'
                onClick={test.action}
                disabled={loading[test.key]}
                sx={{ mb: 3 }}
              >
                {loading[test.key] ? 'Testing...' : 'Run Test'}
              </Button>
              
              {results[test.key] && (
                <Box>
                  <Alert 
                    severity={results[test.key].success ? 'success' : 'error'}
                    sx={{ mb: 2 }}
                  >
                    Status: {results[test.key].status} - {results[test.key].success ? 'Success' : 'Failed'}
                  </Alert>
                  
                  <TextField
                    fullWidth
                    multiline
                    rows={8}
                    label='Response'
                    value={JSON.stringify(results[test.key].data, null, 2)}
                    InputProps={{ readOnly: true }}
                    variant='outlined'
                  />
                </Box>
              )}
            </CardContent>
          </Card>
        ))}
      </Box>
      
      <Card sx={{ mt: 4 }}>
        <CardHeader title='🧪 Test Instructions' />
        <CardContent>
          <Typography variant='body2' sx={{ mb: 2 }}>
            1. <strong>MongoDB Connection Test:</strong> Tests database connectivity
          </Typography>
          <Typography variant='body2' sx={{ mb: 2 }}>
            2. <strong>Login Tests:</strong> Test authentication with valid/invalid credentials
          </Typography>
          <Typography variant='body2' sx={{ mb: 2 }}>
            3. <strong>Protected Routes:</strong> Test API routes that require authentication
          </Typography>
          <Typography variant='body2' sx={{ color: 'warning.main' }}>
            <strong>Note:</strong> Run login tests first to get access tokens for protected routes
          </Typography>
        </CardContent>
      </Card>
    </Box>
  )
}

ApiTestPage.getLayout = page => <BlankLayout>{page}</BlankLayout>

export default ApiTestPage
