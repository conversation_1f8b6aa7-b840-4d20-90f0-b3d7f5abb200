// ** React Imports
import { useState, useEffect } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import Avatar from '@mui/material/Avatar'
import CardHeader from '@mui/material/CardHeader'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'

// ** Icons Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomChip from 'src/@core/components/mui/chip'
import OptionsMenu from 'src/@core/components/option-menu'

const CrmStatistics = () => {
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeCustomers: 0,
    prospects: 0,
    totalDeals: 0
  })

  useEffect(() => {
    // Fetch statistics from API
    const fetchStats = async () => {
      try {
        const token = window.localStorage.getItem('accessToken')
        const response = await fetch('/api/crm/customers', {
          headers: {
            Authorization: token
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          const customers = data.customers || []
          
          setStats({
            totalCustomers: customers.length,
            activeCustomers: customers.filter(c => c.status === 'active').length,
            prospects: customers.filter(c => c.status === 'prospect').length,
            totalDeals: customers.length * 2 // Mock calculation
          })
        }
      } catch (error) {
        console.error('Error fetching stats:', error)
      }
    }

    fetchStats()
  }, [])

  const statisticsData = [
    {
      stats: stats.totalCustomers,
      title: 'Total Customers',
      color: 'primary',
      icon: <Icon icon='mdi:account-group' />
    },
    {
      stats: stats.activeCustomers,
      title: 'Active Customers',
      color: 'success',
      icon: <Icon icon='mdi:account-check' />
    },
    {
      stats: stats.prospects,
      title: 'Prospects',
      color: 'warning',
      icon: <Icon icon='mdi:account-clock' />
    },
    {
      stats: stats.totalDeals,
      title: 'Total Deals',
      color: 'info',
      icon: <Icon icon='mdi:handshake' />
    }
  ]

  return (
    <Grid container spacing={6}>
      {statisticsData.map((item, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <Box key={index} sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              variant='rounded'
              sx={{
                mr: 3,
                width: 44,
                height: 44,
                boxShadow: 3,
                color: 'common.white',
                backgroundColor: `${item.color}.main`
              }}
            >
              {item.icon}
            </Avatar>
            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
              <Typography variant='caption'>
                {item.title}
              </Typography>
              <Typography variant='h6'>
                {item.stats}
              </Typography>
            </Box>
          </Box>
        </Grid>
      ))}
    </Grid>
  )
}

export default CrmStatistics
