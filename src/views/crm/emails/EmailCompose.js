// ** React Imports
import { useState, useEffect } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import TextField from '@mui/material/TextField'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Typography from '@mui/material/Typography'
import Alert from '@mui/material/Alert'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'

// ** Third Party Imports
import { useForm, Controller } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

const schema = yup.object().shape({
  to: yup.string().email('Invalid email').required('Email is required'),
  subject: yup.string().required('Subject is required'),
  message: yup.string().required('Message is required')
})

const EmailCompose = ({ customer, onClose, onSent }) => {
  // ** State
  const [templates, setTemplates] = useState([])
  const [selectedTemplate, setSelectedTemplate] = useState('')
  const [sending, setSending] = useState(false)
  const [alert, setAlert] = useState(null)

  // ** Form
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: {
      to: customer?.email || '',
      subject: '',
      message: ''
    },
    resolver: yupResolver(schema)
  })

  const watchedMessage = watch('message')

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      const token = window.localStorage.getItem('accessToken')
      const response = await fetch('/api/crm/emails/templates', {
        headers: {
          Authorization: token
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setTemplates(data.templates)
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
    }
  }

  const handleTemplateChange = (templateId) => {
    setSelectedTemplate(templateId)
    
    if (templateId) {
      const template = templates.find(t => t.id === templateId)
      if (template) {
        // Replace variables with customer data
        let subject = template.subject
        let content = template.content
        
        if (customer) {
          subject = subject.replace(/{{customerName}}/g, customer.name)
          subject = subject.replace(/{{companyName}}/g, customer.company)
          
          content = content.replace(/{{customerName}}/g, customer.name)
          content = content.replace(/{{companyName}}/g, customer.company)
          content = content.replace(/{{customMessage}}/g, '')
        }
        
        setValue('subject', subject)
        setValue('message', content)
      }
    }
  }

  const onSubmit = async (data) => {
    setSending(true)
    setAlert(null)
    
    try {
      const token = window.localStorage.getItem('accessToken')
      const response = await fetch('/api/crm/emails/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token
        },
        body: JSON.stringify({
          customerId: customer?._id,
          to: data.to,
          subject: data.subject,
          message: data.message,
          template: selectedTemplate || null
        })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        setAlert({
          type: 'success',
          message: 'Email sent successfully!'
        })
        
        // Call onSent callback if provided
        if (onSent) {
          onSent(result)
        }
        
        // Close after 2 seconds
        setTimeout(() => {
          if (onClose) onClose()
        }, 2000)
      } else {
        setAlert({
          type: 'error',
          message: result.error || 'Failed to send email'
        })
      }
    } catch (error) {
      setAlert({
        type: 'error',
        message: 'Network error. Please try again.'
      })
    } finally {
      setSending(false)
    }
  }

  return (
    <Card>
      <CardHeader
        title={`Compose Email${customer ? ` to ${customer.name}` : ''}`}
        action={
          onClose && (
            <IconButton onClick={onClose}>
              <Icon icon='mdi:close' />
            </IconButton>
          )
        }
      />
      <Divider />
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={4}>
            {/* Template Selection */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Email Template (Optional)</InputLabel>
                <Select
                  value={selectedTemplate}
                  label='Email Template (Optional)'
                  onChange={(e) => handleTemplateChange(e.target.value)}
                >
                  <MenuItem value=''>
                    <em>No Template</em>
                  </MenuItem>
                  {templates.map((template) => (
                    <MenuItem key={template.id} value={template.id}>
                      <Box>
                        <Typography variant='body2'>{template.name}</Typography>
                        <Typography variant='caption' color='text.secondary'>
                          {template.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* To Field */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name='to'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label='To'
                      error={Boolean(errors.to)}
                      helperText={errors.to?.message}
                      InputProps={{
                        startAdornment: customer && (
                          <Chip
                            size='small'
                            label={customer.name}
                            color='primary'
                            variant='outlined'
                            sx={{ mr: 1 }}
                          />
                        )
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            {/* Subject Field */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name='subject'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label='Subject'
                      error={Boolean(errors.subject)}
                      helperText={errors.subject?.message}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            {/* Message Field */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name='message'
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label='Message'
                      multiline
                      rows={8}
                      error={Boolean(errors.message)}
                      helperText={errors.message?.message}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            {/* Character Count */}
            <Grid item xs={12}>
              <Typography variant='caption' color='text.secondary'>
                Characters: {watchedMessage?.length || 0}
              </Typography>
            </Grid>

            {/* Alert */}
            {alert && (
              <Grid item xs={12}>
                <Alert severity={alert.type}>{alert.message}</Alert>
              </Grid>
            )}

            {/* Action Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                {onClose && (
                  <Button variant='outlined' onClick={onClose}>
                    Cancel
                  </Button>
                )}
                <Button
                  type='submit'
                  variant='contained'
                  disabled={sending}
                  startIcon={sending ? <Icon icon='mdi:loading' className='animate-spin' /> : <Icon icon='mdi:send' />}
                >
                  {sending ? 'Sending...' : 'Send Email'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  )
}

export default EmailCompose
