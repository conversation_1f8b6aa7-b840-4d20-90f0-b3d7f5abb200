{
  // JS
  "javascript.updateImportsOnFileMove.enabled": "always",
  // JSON
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  // Extension: Git
  "git.rebaseWhenSync": true,
  "git.autofetch": true,
  "git.enableSmartCommit": true,
  // Extension: Prettier
  "prettier.requireConfig": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  // Extension: ESLint
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  "eslint.validate": [
    "javascript",
    "typescript",
    "javascriptreact",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  // Package manager Configs
  "eslint.packageManager": "yarn",
  "npm.packageManager": "yarn",
  // Extension: cSpell
  "cSpell.words": [
    "Customizer"
  ],
  "search-node-modules.path": "node_modules",
  "markdown.extension.toc.updateOnSave": false
}